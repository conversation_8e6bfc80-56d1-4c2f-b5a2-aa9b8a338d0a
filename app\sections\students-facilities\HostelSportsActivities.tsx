"use client";

import Image from "next/image";

const HostelSportsActivities = () => {
  return (
    <section className="bg-white py-10 px-4">
      <div className="container max-w-7xl mx-auto space-y-6">
        <h2 className="text-custom-green text-center">
          Hostel Sports Activities
        </h2>
        <p className="text-custom-new-green text-center">
          Aliquet sed nulla tincidunt pulvinar sed fames sit facilisis dictumst.
          Ornare faucibus quis velit fringilla aliquam ultricies. Malesuada ut
          aliquam at ac est nisi, interdum etiam dignissim. Sed ut vestibulum
          eget purus ornare. Risus elit et fringilla habitant ut facilisi.
        </p>
        <div className="flex flex-col md:flex-row gap-6">
          {/* Sports Indoor */}
          <div className="relative h-[200px] md:h-[350px] border-[3px] border-custom-green flex-1 rounded-xl overflow-hidden shadow-md group cursor-pointer">
            <Image
              src="/sports1.webp"
              alt="Sports Indoor"
              width={500}
              height={400}
              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            />
            {/* Default overlay - only title */}
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-green-800 to-transparent p-4 font-ramilas text-white font-bold text-center text-lg group-hover:opacity-0 transition-opacity duration-300">
              <h3 className="font-semibold">Sports Indoor</h3>
            </div>
            {/* Hover overlay - title and description */}
            <div className="absolute inset-0 bg-green-800/90 p-4 font-ramilas text-white text-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-center">
              <h3 className="font-semibold mb-4 text-xl">Sports Indoor</h3>
              <p className="text-sm leading-relaxed">
                To encourage the sports spirits among the inmates, the Annual
                Sports Meet will be held. To initiate the activities a meeting
                was held on with the students representative of UG and PG
                programmes.
              </p>
            </div>
          </div>

          {/* Sports Outdoor */}
          <div className="h-[200px] md:h-[350px] border-[3px] border-custom-green relative flex-1 rounded-xl overflow-hidden shadow-md group cursor-pointer">
            <Image
              src="/sports2.webp"
              alt="Sports Outdoor"
              width={500}
              height={400}
              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            />
            {/* Default overlay - only title */}
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-green-800 to-transparent p-4 font-ramilas text-white font-bold text-center text-lg group-hover:opacity-0 transition-opacity duration-300">
              <h3 className="font-semibold">Sports Outdoor</h3>
            </div>
            {/* Hover overlay - title and description */}
            <div className="absolute inset-0 bg-green-800/90 p-4 font-ramilas text-white text-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-center">
              <h3 className="font-semibold mb-4 text-xl">Sports Outdoor</h3>
              <p className="text-sm leading-relaxed">
                Our outdoor sports facilities provide students with opportunities
                to engage in various athletic activities including cricket, football,
                basketball, and track events. These facilities promote physical
                fitness and team spirit among students.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HostelSportsActivities;
